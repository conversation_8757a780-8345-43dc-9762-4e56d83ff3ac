// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
import { getStorage } from 'firebase/storage';
import { getFirestore } from "firebase/firestore";
import { getDatabase } from "firebase/database";
import { getAuth } from "firebase/auth";

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyCCyDbFigvyA53SEPXBNkNJpRNr-0Vp7Qo",
  authDomain: "toika-369.firebaseapp.com",
  databaseURL: "https://toika-369-default-rtdb.firebaseio.com",
  projectId: "toika-369",
  storageBucket: "toika-369.appspot.com",
  messagingSenderId: "300804286264",
  appId: "1:300804286264:web:15b91915784f3bf65403cd",
  measurementId: "G-N9EE86JXMB"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const analytics = getAnalytics(app);
export const storage = getStorage(app);
export const db = getFirestore(app);
export const realtimeDb = getDatabase(app);
export const auth = getAuth(app);

export default app;


